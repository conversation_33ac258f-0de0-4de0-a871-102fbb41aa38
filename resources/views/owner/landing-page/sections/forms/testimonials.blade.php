@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="{{ old('title', $content['title'] ?? '') }}" 
                   placeholder="e.g., What Our Customers Say" required>
            <small class="form-text text-muted">The main heading for your testimonials section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <input type="text" class="form-control" id="subtitle" name="subtitle" 
                   value="{{ old('subtitle', $content['subtitle'] ?? '') }}" 
                   placeholder="e.g., Read reviews from our satisfied clients">
            <small class="form-text text-muted">Optional subtitle to provide more context</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="carousel" {{ ($content['layout'] ?? 'carousel') == 'carousel' ? 'selected' : '' }}>Carousel</option>
                <option value="grid" {{ ($content['layout'] ?? 'carousel') == 'grid' ? 'selected' : '' }}>Grid Layout</option>
                <option value="list" {{ ($content['layout'] ?? 'carousel') == 'list' ? 'selected' : '' }}>List Layout</option>
            </select>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="show_ratings" name="show_ratings" value="1" 
                       {{ ($content['show_ratings'] ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="show_ratings">
                    Show Star Ratings
                </label>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="show_photos" name="show_photos" value="1" 
                       {{ ($content['show_photos'] ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="show_photos">
                    Show Customer Photos
                </label>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="auto_rotate" name="auto_rotate" value="1" 
                       {{ ($content['auto_rotate'] ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="auto_rotate">
                    Auto-rotate Testimonials (Carousel only)
                </label>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="rotation_speed">Rotation Speed (seconds)</label>
            <input type="number" class="form-control" id="rotation_speed" name="rotation_speed" 
                   value="{{ old('rotation_speed', $content['rotation_speed'] ?? 5) }}" 
                   min="3" max="10" step="1">
        </div>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    <strong>Note:</strong> Testimonials are automatically pulled from your approved customer reviews. 
    You can manage reviews in the <a href="{{ route('owner.reviews.index') }}" target="_blank">Reviews section</a>.
    To feature specific reviews, mark them as "Featured" in the reviews management area.
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label>Display Settings</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_testimonials">Maximum Testimonials to Show</label>
                                <input type="number" class="form-control" id="max_testimonials" name="max_testimonials" 
                                       value="{{ old('max_testimonials', $content['max_testimonials'] ?? 6) }}" 
                                       min="3" max="20">
                                <small class="form-text text-muted">How many testimonials to display</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="min_rating">Minimum Rating</label>
                                <select class="form-control" id="min_rating" name="min_rating">
                                    <option value="1" {{ ($content['min_rating'] ?? 4) == 1 ? 'selected' : '' }}>1 Star and above</option>
                                    <option value="2" {{ ($content['min_rating'] ?? 4) == 2 ? 'selected' : '' }}>2 Stars and above</option>
                                    <option value="3" {{ ($content['min_rating'] ?? 4) == 3 ? 'selected' : '' }}>3 Stars and above</option>
                                    <option value="4" {{ ($content['min_rating'] ?? 4) == 4 ? 'selected' : '' }}>4 Stars and above</option>
                                    <option value="5" {{ ($content['min_rating'] ?? 4) == 5 ? 'selected' : '' }}>5 Stars only</option>
                                </select>
                                <small class="form-text text-muted">Only show reviews with this rating or higher</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="featured_only" name="featured_only" value="1" 
                                       {{ ($content['featured_only'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="featured_only">
                                    Show only featured reviews
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_service_name" name="show_service_name" value="1" 
                                       {{ ($content['show_service_name'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_service_name">
                                    Show service name in testimonials
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const layoutSelect = document.getElementById('layout');
    const autoRotateCheck = document.getElementById('auto_rotate');
    const rotationSpeedInput = document.getElementById('rotation_speed');
    
    function toggleCarouselOptions() {
        const isCarousel = layoutSelect.value === 'carousel';
        autoRotateCheck.closest('.form-group').style.display = isCarousel ? 'block' : 'none';
        rotationSpeedInput.closest('.form-group').style.display = isCarousel ? 'block' : 'none';
    }
    
    layoutSelect.addEventListener('change', toggleCarouselOptions);
    toggleCarouselOptions(); // Initial call
});
</script>
