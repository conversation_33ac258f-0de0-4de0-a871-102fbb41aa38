@php
    $content = $section->content_data ?? $section->getDefaultContentForType();
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="title">Section Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="{{ old('title', $content['title'] ?? '') }}" 
                   placeholder="e.g., Frequently Asked Questions" required>
            <small class="form-text text-muted">The main heading for your FAQ section</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            <label for="subtitle">Subtitle</label>
            <input type="text" class="form-control" id="subtitle" name="subtitle" 
                   value="{{ old('subtitle', $content['subtitle'] ?? '') }}" 
                   placeholder="e.g., Find answers to common questions">
            <small class="form-text text-muted">Optional subtitle to provide more context</small>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <label for="layout">Layout Style</label>
            <select class="form-control" id="layout" name="layout">
                <option value="accordion" {{ ($content['layout'] ?? 'accordion') == 'accordion' ? 'selected' : '' }}>Accordion (Expandable)</option>
                <option value="list" {{ ($content['layout'] ?? 'accordion') == 'list' ? 'selected' : '' }}>Simple List</option>
                <option value="grid" {{ ($content['layout'] ?? 'accordion') == 'grid' ? 'selected' : '' }}>Grid Layout</option>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="search_enabled" name="search_enabled" value="1" 
                       {{ ($content['search_enabled'] ?? true) ? 'checked' : '' }}>
                <label class="form-check-label" for="search_enabled">
                    Enable FAQ Search
                </label>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <label>FAQ Questions & Answers</label>
        <div id="faqs-container">
            @if(isset($content['questions']) && is_array($content['questions']))
                @foreach($content['questions'] as $index => $faq)
                    <div class="faq-item border rounded p-3 mb-3" data-index="{{ $index }}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">FAQ {{ $index + 1 }}</h6>
                            <button type="button" class="btn btn-sm btn-danger remove-faq">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Question</label>
                                    <input type="text" class="form-control" name="questions[{{ $index }}][question]" 
                                           value="{{ $faq['question'] ?? '' }}" 
                                           placeholder="Enter the question" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Category (optional)</label>
                                    <input type="text" class="form-control" name="questions[{{ $index }}][category]" 
                                           value="{{ $faq['category'] ?? '' }}" 
                                           placeholder="e.g., Booking, Policies">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Answer</label>
                                    <textarea class="form-control" name="questions[{{ $index }}][answer]" 
                                              rows="3" placeholder="Enter the answer" required>{{ $faq['answer'] ?? '' }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Sort Order</label>
                                    <input type="number" class="form-control" name="questions[{{ $index }}][sort_order]" 
                                           value="{{ $faq['sort_order'] ?? ($index + 1) }}" 
                                           min="1">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" 
                                               name="questions[{{ $index }}][featured]" value="1" 
                                               {{ ($faq['featured'] ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label">
                                            Featured (Show first)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
        
        <button type="button" class="btn btn-outline-primary" id="add-faq">
            <i class="fas fa-plus mr-1"></i>
            Add FAQ
        </button>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="form-group">
            <label>Additional Settings</label>
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_contact_cta" name="show_contact_cta" value="1" 
                                       {{ ($content['show_contact_cta'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_contact_cta">
                                    Show "Still have questions?" contact section
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_expand_all" name="allow_expand_all" value="1" 
                                       {{ ($content['allow_expand_all'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="allow_expand_all">
                                    Show "Expand All" / "Collapse All" buttons
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="contact_cta_text">Contact CTA Text</label>
                                <input type="text" class="form-control" id="contact_cta_text" name="contact_cta_text" 
                                       value="{{ old('contact_cta_text', $content['contact_cta_text'] ?? 'Still have questions? Contact us!') }}" 
                                       placeholder="Still have questions? Contact us!">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let faqIndex = {{ count($content['questions'] ?? []) }};
    
    document.getElementById('add-faq').addEventListener('click', function() {
        const container = document.getElementById('faqs-container');
        const faqHtml = `
            <div class="faq-item border rounded p-3 mb-3" data-index="${faqIndex}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">FAQ ${faqIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-faq">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Question</label>
                            <input type="text" class="form-control" name="questions[${faqIndex}][question]" 
                                   placeholder="Enter the question" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Category (optional)</label>
                            <input type="text" class="form-control" name="questions[${faqIndex}][category]" 
                                   placeholder="e.g., Booking, Policies">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Answer</label>
                            <textarea class="form-control" name="questions[${faqIndex}][answer]" 
                                      rows="3" placeholder="Enter the answer" required></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Sort Order</label>
                            <input type="number" class="form-control" name="questions[${faqIndex}][sort_order]" 
                                   value="${faqIndex + 1}" min="1">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" 
                                       name="questions[${faqIndex}][featured]" value="1">
                                <label class="form-check-label">
                                    Featured (Show first)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', faqHtml);
        faqIndex++;
    });
    
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-faq')) {
            e.target.closest('.faq-item').remove();
        }
    });
});
</script>
