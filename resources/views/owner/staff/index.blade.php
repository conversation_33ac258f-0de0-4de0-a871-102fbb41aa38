@extends('layouts.owner')

@section('title', 'Staff Management')

@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Staff Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Staff</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Team Members</h3>
                <div class="card-tools">
                    <a href="{{ route('owner.staff.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus mr-1"></i>
                        Add Staff Member
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($staff->count() > 0)
                    <div class="row">
                        @foreach($staff as $member)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            @if($member->photo)
                                                <img src="{{ $member->photo }}" alt="{{ $member->name }}" 
                                                     class="rounded-circle" width="80" height="80">
                                            @else
                                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                     style="width: 80px; height: 80px;">
                                                    <i class="fas fa-user fa-2x text-white"></i>
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <h5 class="card-title">{{ $member->name }}</h5>
                                        <p class="text-muted">{{ $member->position }}</p>
                                        
                                        @if($member->specializations)
                                            <div class="mb-2">
                                                @foreach(explode(',', $member->specializations) as $specialization)
                                                    <span class="badge badge-secondary">{{ trim($specialization) }}</span>
                                                @endforeach
                                            </div>
                                        @endif
                                        
                                        @if($member->years_experience)
                                            <small class="text-muted">{{ $member->years_experience }} years experience</small>
                                        @endif
                                        
                                        <div class="mt-3">
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('owner.staff.show', $member) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('owner.staff.edit', $member) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="{{ route('owner.staff.destroy', $member) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure you want to remove this staff member?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-2">
                                            @if($member->is_active)
                                                <span class="badge badge-success">Active</span>
                                            @else
                                                <span class="badge badge-secondary">Inactive</span>
                                            @endif
                                            
                                            @if($member->show_on_landing_page)
                                                <span class="badge badge-info">On Landing Page</span>
                                            @endif
                                            
                                            @if($member->accepts_bookings)
                                                <span class="badge badge-warning">Accepts Bookings</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <h4>No Staff Members Yet</h4>
                        <p class="text-muted">
                            Add your team members to showcase them on your landing page and manage bookings.
                        </p>
                        <a href="{{ route('owner.staff.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus mr-1"></i>
                            Add First Staff Member
                        </a>
                    </div>
                @endif
            </div>
        </div>
        
        @if($staff->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Total Staff</span>
                                    <span class="info-box-number">{{ $staff->count() }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Active</span>
                                    <span class="info-box-number">{{ $staff->where('is_active', true)->count() }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning"><i class="fas fa-calendar"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Accept Bookings</span>
                                    <span class="info-box-number">{{ $staff->where('accepts_bookings', true)->count() }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary"><i class="fas fa-globe"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">On Landing Page</span>
                                    <span class="info-box-number">{{ $staff->where('show_on_landing_page', true)->count() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</section>
@endsection
