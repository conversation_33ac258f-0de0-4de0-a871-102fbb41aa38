@extends('layouts.owner')

@section('title', 'Reviews Management')

@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Reviews Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Reviews</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Stats Cards -->
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>{{ $stats['total_reviews'] }}</h3>
                        <p>Total Reviews</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>{{ number_format($stats['average_rating'], 1) }}</h3>
                        <p>Average Rating</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3>{{ $stats['pending_reviews'] }}</h3>
                        <p>Pending Reviews</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3>{{ $stats['featured_reviews'] }}</h3>
                        <p>Featured Reviews</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-bookmark"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Reviews</h3>
                <div class="card-tools">
                    <a href="{{ route('owner.reviews.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus mr-1"></i>
                        Add Review
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" class="mb-3">
                    <div class="row">
                        <div class="col-md-3">
                            <input type="text" name="search" class="form-control" placeholder="Search reviews..." 
                                   value="{{ request('search') }}">
                        </div>
                        <div class="col-md-2">
                            <select name="rating" class="form-control">
                                <option value="">All Ratings</option>
                                @for($i = 5; $i >= 1; $i--)
                                    <option value="{{ $i }}" {{ request('rating') == $i ? 'selected' : '' }}>
                                        {{ $i }} Star{{ $i > 1 ? 's' : '' }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="status" class="form-control">
                                <option value="">All Status</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="service_id" class="form-control">
                                <option value="">All Services</option>
                                @foreach($services as $service)
                                    <option value="{{ $service->id }}" {{ request('service_id') == $service->id ? 'selected' : '' }}>
                                        {{ $service->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="{{ route('owner.reviews.index') }}" class="btn btn-secondary">Clear</a>
                        </div>
                    </div>
                </form>

                <!-- Reviews Table -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Service</th>
                                <th>Rating</th>
                                <th>Review</th>
                                <th>Status</th>
                                <th>Featured</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($reviews as $review)
                                <tr>
                                    <td>
                                        <strong>{{ $review->customer_name }}</strong><br>
                                        <small class="text-muted">{{ $review->customer_email }}</small>
                                    </td>
                                    <td>{{ $review->service->name ?? 'N/A' }}</td>
                                    <td>
                                        <div class="rating">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $review->rating ? 'text-warning' : 'text-muted' }}"></i>
                                            @endfor
                                            <span class="ml-1">({{ $review->rating }})</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="review-text">
                                            {{ Str::limit($review->review_text, 100) }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ $review->status == 'approved' ? 'success' : ($review->status == 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($review->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-{{ $review->is_featured ? 'warning' : 'outline-secondary' }} toggle-featured" 
                                                data-id="{{ $review->id }}" data-featured="{{ $review->is_featured ? 1 : 0 }}">
                                            <i class="fas fa-star"></i>
                                        </button>
                                    </td>
                                    <td>{{ $review->created_at->format('M d, Y') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('owner.reviews.show', $review) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('owner.reviews.edit', $review) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form action="{{ route('owner.reviews.destroy', $review) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                        onclick="return confirm('Are you sure you want to delete this review?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                        
                                        @if($review->status == 'pending')
                                            <div class="btn-group mt-1" role="group">
                                                <button type="button" class="btn btn-sm btn-success update-status" 
                                                        data-id="{{ $review->id }}" data-status="approved">
                                                    Approve
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger update-status" 
                                                        data-id="{{ $review->id }}" data-status="rejected">
                                                    Reject
                                                </button>
                                            </div>
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="py-4">
                                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                            <h5>No reviews found</h5>
                                            <p class="text-muted">No reviews match your current filters.</p>
                                            <a href="{{ route('owner.reviews.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus mr-1"></i>
                                                Add First Review
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($reviews->hasPages())
                    <div class="d-flex justify-content-center">
                        {{ $reviews->appends(request()->query())->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle featured status
    $('.toggle-featured').click(function() {
        const reviewId = $(this).data('id');
        const button = $(this);
        
        $.post(`/owner/reviews/${reviewId}/toggle-featured`, {
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                if (response.is_featured) {
                    button.removeClass('btn-outline-secondary').addClass('btn-warning');
                } else {
                    button.removeClass('btn-warning').addClass('btn-outline-secondary');
                }
                
                toastr.success(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to update featured status');
        });
    });
    
    // Update review status
    $('.update-status').click(function() {
        const reviewId = $(this).data('id');
        const status = $(this).data('status');
        const button = $(this);
        
        $.ajax({
            url: `/owner/reviews/${reviewId}/status`,
            method: 'PATCH',
            data: {
                status: status,
                _token: '{{ csrf_token() }}'
            }
        })
        .done(function(response) {
            if (response.success) {
                location.reload();
            }
        })
        .fail(function() {
            toastr.error('Failed to update review status');
        });
    });
});
</script>
@endpush
